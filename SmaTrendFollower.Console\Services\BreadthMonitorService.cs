using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time market breadth monitoring service implementation
/// Tracks advancing/declining stock count across dynamic universe
/// Calculates % above 50SMA/200SMA, new highs/lows
/// Injects breadth context into signal engine with real-time updates
/// </summary>
public sealed class BreadthMonitorService : IBreadthMonitorService, IDisposable
{
    private readonly IDynamicUniverseProvider _universeProvider;
    private readonly IMarketDataService _marketDataService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITickStreamService _tickStreamService;
    private readonly ILogger<BreadthMonitorService> _logger;
    private readonly BreadthMonitorConfig _config;
    
    private readonly Timer _updateTimer;
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    private readonly ConcurrentDictionary<string, decimal> _lastPrices = new();
    private readonly ConcurrentDictionary<string, BreadthSymbolData> _symbolData = new();
    
    private BreadthMonitorStatus _status = BreadthMonitorStatus.Stopped;
    private BreadthRegime _currentRegime = BreadthRegime.Healthy;
    private RealTimeBreadthAnalysis? _lastAnalysis;
    private DateTime _monitoringStartedAt;
    private int _totalUpdates;
    private int _failedUpdates;
    private bool _disposed;

    public BreadthMonitorService(
        IDynamicUniverseProvider universeProvider,
        IMarketDataService marketDataService,
        IOptimizedRedisConnectionService redisService,
        ITickStreamService tickStreamService,
        IConfiguration configuration,
        ILogger<BreadthMonitorService> logger)
    {
        _universeProvider = universeProvider ?? throw new ArgumentNullException(nameof(universeProvider));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new BreadthMonitorConfig();
        configuration.GetSection("BreadthMonitor").Bind(_config);

        // Set up timer for periodic updates
        _updateTimer = new Timer(UpdateBreadthCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    // === Events ===
    
    public event EventHandler<BreadthAnalysisUpdatedEventArgs>? BreadthAnalysisUpdated;
    public event EventHandler<BreadthRegimeChangedEventArgs>? BreadthRegimeChanged;
    public event EventHandler<BreadthDivergenceEventArgs>? BreadthDivergenceDetected;
    public event EventHandler<ExtremeBreadthEventArgs>? ExtremeBreadthDetected;

    // === Core Methods ===

    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(BreadthMonitorService));

        if (_status == BreadthMonitorStatus.Active)
            return;

        try
        {
            _status = BreadthMonitorStatus.Starting;
            _logger.LogInformation("Starting BreadthMonitorService...");

            _monitoringStartedAt = DateTime.UtcNow;

            // Subscribe to real-time tick updates if enabled
            if (_config.EnableRealTimeUpdates)
            {
                await SubscribeToTickUpdatesAsync();
            }

            // Perform initial breadth analysis
            await RefreshBreadthAnalysisAsync(cancellationToken);

            // Start periodic updates
            _updateTimer.Change(
                TimeSpan.FromSeconds(_config.UpdateIntervalSeconds),
                TimeSpan.FromSeconds(_config.UpdateIntervalSeconds));

            _status = BreadthMonitorStatus.Active;
            _logger.LogInformation("BreadthMonitorService started successfully");
        }
        catch (Exception ex)
        {
            _status = BreadthMonitorStatus.Error;
            _logger.LogError(ex, "Failed to start BreadthMonitorService");
            throw;
        }
    }

    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || _status == BreadthMonitorStatus.Stopped)
            return;

        try
        {
            _logger.LogInformation("Stopping BreadthMonitorService...");

            // Stop timer
            _updateTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // Unsubscribe from tick updates
            await UnsubscribeFromTickUpdatesAsync();

            _status = BreadthMonitorStatus.Stopped;
            _logger.LogInformation("BreadthMonitorService stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping BreadthMonitorService");
        }
    }

    public async Task<RealTimeBreadthAnalysis> GetCurrentBreadthAsync(CancellationToken cancellationToken = default)
    {
        var cached = await GetCachedBreadthAsync(cancellationToken);
        if (cached != null && IsCacheValid(cached))
        {
            return cached;
        }

        return await RefreshBreadthAnalysisAsync(cancellationToken);
    }

    public async Task<RealTimeBreadthAnalysis?> GetCachedBreadthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var breadthJson = await database.StringGetAsync("breadth:analysis:current");
            
            if (!breadthJson.HasValue)
                return null;

            return JsonSerializer.Deserialize<RealTimeBreadthAnalysis>(breadthJson!);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get cached breadth analysis");
            return null;
        }
    }

    public async Task<RealTimeBreadthAnalysis> RefreshBreadthAnalysisAsync(CancellationToken cancellationToken = default)
    {
        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogDebug("Refreshing breadth analysis...");

            // Get universe symbols
            var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
            var symbols = universe.Take(1000).ToList(); // Limit for performance

            if (symbols.Count < _config.MinUniverseSize)
            {
                _logger.LogWarning("Universe size {Count} is below minimum {Min}", symbols.Count, _config.MinUniverseSize);
            }

            // Perform all breadth analyses in parallel
            var advanceDeclineTask = CalculateAdvanceDeclineStatsAsync(symbols, cancellationToken);
            var movingAverageTask = CalculateMovingAverageBreadthAsync(symbols, cancellationToken);
            var newHighsLowsTask = CalculateNewHighsLowsStatsAsync(symbols, cancellationToken);
            var momentumTask = CalculateBreadthMomentumAsync(symbols, cancellationToken);
            var divergenceTask = CalculateBreadthDivergenceAsync(symbols, cancellationToken);

            await Task.WhenAll(advanceDeclineTask, movingAverageTask, newHighsLowsTask, momentumTask, divergenceTask);

            var advanceDecline = await advanceDeclineTask;
            var movingAverage = await movingAverageTask;
            var newHighsLows = await newHighsLowsTask;
            var momentum = await momentumTask;
            var divergence = await divergenceTask;

            // Calculate overall breadth score and regime
            var breadthScore = CalculateOverallBreadthScore(advanceDecline, movingAverage, newHighsLows, momentum);
            var regime = ClassifyBreadthRegime(breadthScore, advanceDecline, movingAverage);
            var signalStrength = CalculateSignalStrength(breadthScore, regime);

            var analysis = new RealTimeBreadthAnalysis
            {
                AnalyzedAt = DateTime.UtcNow,
                UniverseSize = symbols.Count,
                AdvanceDecline = advanceDecline,
                MovingAverageBreadth = movingAverage,
                NewHighsLows = newHighsLows,
                Momentum = momentum,
                Divergence = divergence,
                Regime = regime,
                SignalStrength = signalStrength,
                OverallBreadthScore = breadthScore,
                Summary = GenerateBreadthSummary(regime, signalStrength, breadthScore)
            };

            // Check for regime change
            if (regime != _currentRegime)
            {
                var previousRegime = _currentRegime;
                _currentRegime = regime;

                BreadthRegimeChanged?.Invoke(this, new BreadthRegimeChangedEventArgs
                {
                    PreviousRegime = previousRegime,
                    NewRegime = regime,
                    ChangedAt = DateTime.UtcNow,
                    Reason = $"Breadth score: {breadthScore:F2}, A/D: {advanceDecline.AdvanceDeclinePercent:F1}%",
                    Confidence = CalculateRegimeConfidence(analysis)
                });

                _logger.LogInformation("Breadth regime changed from {Previous} to {New} (Score: {Score:F2})",
                    previousRegime, regime, breadthScore);
            }

            // Check for extreme breadth conditions
            await CheckForExtremeBreadthConditions(analysis);

            // Check for divergence signals
            await CheckForDivergenceSignals(analysis);

            // Cache the result
            await CacheBreadthAnalysisAsync(analysis);

            _lastAnalysis = analysis;
            _totalUpdates++;
            stopwatch.Stop();

            BreadthAnalysisUpdated?.Invoke(this, new BreadthAnalysisUpdatedEventArgs
            {
                Analysis = analysis,
                UpdatedAt = DateTime.UtcNow,
                UpdateDuration = stopwatch.Elapsed
            });

            _logger.LogDebug("Breadth analysis completed in {Duration}ms (Score: {Score:F2}, Regime: {Regime})",
                stopwatch.ElapsedMilliseconds, breadthScore, regime);

            return analysis;
        }
        catch (Exception ex)
        {
            _failedUpdates++;
            _logger.LogError(ex, "Error refreshing breadth analysis");
            throw;
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    // === Analysis Methods ===

    public async Task<AdvanceDeclineStats> GetAdvanceDeclineStatsAsync(CancellationToken cancellationToken = default)
    {
        var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
        var symbols = universe.Take(1000).ToList();
        return await CalculateAdvanceDeclineStatsAsync(symbols, cancellationToken);
    }

    public async Task<MovingAverageBreadthStats> GetMovingAverageBreadthAsync(CancellationToken cancellationToken = default)
    {
        var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
        var symbols = universe.Take(1000).ToList();
        return await CalculateMovingAverageBreadthAsync(symbols, cancellationToken);
    }

    public async Task<NewHighsLowsStats> GetNewHighsLowsStatsAsync(CancellationToken cancellationToken = default)
    {
        var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
        var symbols = universe.Take(1000).ToList();
        return await CalculateNewHighsLowsStatsAsync(symbols, cancellationToken);
    }

    public async Task<BreadthMomentumAnalysis> GetBreadthMomentumAsync(CancellationToken cancellationToken = default)
    {
        var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
        var symbols = universe.Take(1000).ToList();
        return await CalculateBreadthMomentumAsync(symbols, cancellationToken);
    }

    public async Task<BreadthDivergenceAnalysis> GetBreadthDivergenceAsync(CancellationToken cancellationToken = default)
    {
        var universe = await _universeProvider.GetCachedUniverseAsync(cancellationToken);
        var symbols = universe.Take(1000).ToList();
        return await CalculateBreadthDivergenceAsync(symbols, cancellationToken);
    }

    // === Signal Integration ===

    public async Task<BreadthSignalStrength> GetBreadthSignalStrengthAsync(CancellationToken cancellationToken = default)
    {
        var analysis = await GetCurrentBreadthAsync(cancellationToken);
        return analysis.SignalStrength;
    }

    public async Task<bool> SupportsBullishSignalsAsync(CancellationToken cancellationToken = default)
    {
        var analysis = await GetCurrentBreadthAsync(cancellationToken);
        return analysis.Regime == BreadthRegime.Healthy && 
               analysis.SignalStrength >= BreadthSignalStrength.Neutral &&
               analysis.OverallBreadthScore > 0.3m;
    }

    public async Task<bool> SuggestsDefensivePositioningAsync(CancellationToken cancellationToken = default)
    {
        var analysis = await GetCurrentBreadthAsync(cancellationToken);
        return analysis.Regime == BreadthRegime.Weak || 
               analysis.Regime == BreadthRegime.Deteriorating ||
               analysis.OverallBreadthScore < 0.2m;
    }

    public async Task<decimal> GetBreadthAdjustedPositionSizeAsync(decimal baseSize, CancellationToken cancellationToken = default)
    {
        var analysis = await GetCurrentBreadthAsync(cancellationToken);
        
        var adjustment = analysis.SignalStrength switch
        {
            BreadthSignalStrength.VeryStrong => 1.2m,
            BreadthSignalStrength.Strong => 1.1m,
            BreadthSignalStrength.Neutral => 1.0m,
            BreadthSignalStrength.Weak => 0.8m,
            BreadthSignalStrength.VeryWeak => 0.6m,
            _ => 1.0m
        };

        return baseSize * adjustment;
    }

    // === Status and Configuration ===

    public BreadthMonitorStatus GetStatus() => _status;

    public int GetMonitoredUniverseSize()
    {
        return _lastAnalysis?.UniverseSize ?? 0;
    }

    public async Task UpdateConfigurationAsync(BreadthMonitorConfig config)
    {
        // Update configuration (implementation would update _config)
        await Task.CompletedTask;
    }

    public async Task<BreadthMonitoringStats> GetMonitoringStatsAsync()
    {
        var totalTime = _status == BreadthMonitorStatus.Active ? DateTime.UtcNow - _monitoringStartedAt : TimeSpan.Zero;
        var successRate = _totalUpdates > 0 ? (decimal)(_totalUpdates - _failedUpdates) / _totalUpdates : 0m;

        return new BreadthMonitoringStats
        {
            LastUpdate = _lastAnalysis?.AnalyzedAt ?? DateTime.MinValue,
            TotalUpdates = _totalUpdates,
            FailedUpdates = _failedUpdates,
            SuccessRate = successRate,
            MonitoringStartedAt = _monitoringStartedAt,
            TotalMonitoringTime = totalTime
        };
    }

    // === Private Helper Methods ===

    private async void UpdateBreadthCallback(object? state)
    {
        try
        {
            await RefreshBreadthAnalysisAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in breadth update callback");
        }
    }

    private async Task SubscribeToTickUpdatesAsync()
    {
        // Subscribe to tick stream for real-time price updates
        // Implementation would subscribe to universe symbols through TickStreamService
        await Task.CompletedTask;
    }

    private async Task UnsubscribeFromTickUpdatesAsync()
    {
        // Unsubscribe from tick stream
        await Task.CompletedTask;
    }

    private bool IsCacheValid(RealTimeBreadthAnalysis analysis)
    {
        var age = DateTime.UtcNow - analysis.AnalyzedAt;
        return age.TotalMinutes <= _config.CacheExpiryMinutes;
    }

    private async Task CacheBreadthAnalysisAsync(RealTimeBreadthAnalysis analysis)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var json = JsonSerializer.Serialize(analysis);
            await database.StringSetAsync(
                "breadth:analysis:current",
                json,
                TimeSpan.FromMinutes(_config.CacheExpiryMinutes));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache breadth analysis");
        }
    }

    private async Task<AdvanceDeclineStats> CalculateAdvanceDeclineStatsAsync(
        List<string> symbols,
        CancellationToken cancellationToken)
    {
        try
        {
            var advancing = 0;
            var declining = 0;
            var unchanged = 0;

            // Get current prices and compare with previous close
            var tasks = symbols.Select(async symbol =>
            {
                try
                {
                    var endTime = DateTime.UtcNow;
                    var startTime = endTime.AddDays(-2);
                    var bars = await _marketDataService.GetStockBarsAsync(symbol, startTime, endTime);
                    var barsList = bars.ToList();

                    if (barsList.Count >= 2)
                    {
                        var current = barsList.Last();
                        var previous = barsList[^2];
                        var change = current.Close - previous.Close;

                        return change switch
                        {
                            > 0 => 1, // Advancing
                            < 0 => -1, // Declining
                            _ => 0 // Unchanged
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error getting price data for {Symbol}", symbol);
                }
                return 0;
            });

            var results = await Task.WhenAll(tasks);

            advancing = results.Count(r => r == 1);
            declining = results.Count(r => r == -1);
            unchanged = results.Count(r => r == 0);

            var total = advancing + declining + unchanged;
            var advanceDeclineRatio = declining > 0 ? (decimal)advancing / declining : advancing;
            var advanceDeclinePercent = total > 0 ? (decimal)advancing / total * 100 : 0;

            return new AdvanceDeclineStats
            {
                Advancing = advancing,
                Declining = declining,
                Unchanged = unchanged,
                AdvanceDeclineRatio = advanceDeclineRatio,
                AdvanceDeclinePercent = advanceDeclinePercent,
                CumulativeAdvanceDecline = advanceDeclinePercent, // Simplified
                Trend = ClassifyAdvanceDeclineTrend(advanceDeclinePercent)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating advance/decline stats");
            return new AdvanceDeclineStats();
        }
    }

    private async Task<MovingAverageBreadthStats> CalculateMovingAverageBreadthAsync(
        List<string> symbols,
        CancellationToken cancellationToken)
    {
        try
        {
            var above50SMA = 0;
            var above200SMA = 0;

            var tasks = symbols.Select(async symbol =>
            {
                try
                {
                    var endTime = DateTime.UtcNow;
                    var startTime = endTime.AddDays(-250); // Get enough data for 200 SMA
                    var bars = await _marketDataService.GetStockBarsAsync(symbol, startTime, endTime);
                    var barsList = bars.ToList();

                    if (barsList.Count >= 200)
                    {
                        var currentPrice = barsList.Last().Close;
                        var sma50 = barsList.TakeLast(50).Average(b => b.Close);
                        var sma200 = barsList.TakeLast(200).Average(b => b.Close);

                        return new
                        {
                            Above50 = currentPrice > sma50,
                            Above200 = currentPrice > sma200
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error calculating SMA for {Symbol}", symbol);
                }
                return new { Above50 = false, Above200 = false };
            });

            var results = await Task.WhenAll(tasks);

            above50SMA = results.Count(r => r.Above50);
            above200SMA = results.Count(r => r.Above200);

            var total = symbols.Count;
            var percentAbove50SMA = total > 0 ? (decimal)above50SMA / total * 100 : 0;
            var percentAbove200SMA = total > 0 ? (decimal)above200SMA / total * 100 : 0;

            return new MovingAverageBreadthStats
            {
                Above50SMA = above50SMA,
                Below50SMA = total - above50SMA,
                PercentAbove50SMA = percentAbove50SMA,
                Above200SMA = above200SMA,
                Below200SMA = total - above200SMA,
                PercentAbove200SMA = percentAbove200SMA,
                Trend50SMA = ClassifyMovingAverageTrend(percentAbove50SMA),
                Trend200SMA = ClassifyMovingAverageTrend(percentAbove200SMA)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating moving average breadth");
            return new MovingAverageBreadthStats();
        }
    }

    private async Task<NewHighsLowsStats> CalculateNewHighsLowsStatsAsync(
        List<string> symbols,
        CancellationToken cancellationToken)
    {
        try
        {
            var newHighs52Week = 0;
            var newLows52Week = 0;
            var newHighs20Day = 0;
            var newLows20Day = 0;

            var tasks = symbols.Select(async symbol =>
            {
                try
                {
                    var endTime = DateTime.UtcNow;
                    var startTime = endTime.AddDays(-365); // Get 1 year of data
                    var bars = await _marketDataService.GetStockBarsAsync(symbol, startTime, endTime);
                    var barsList = bars.ToList();

                    if (barsList.Count >= 252) // ~1 year of trading days
                    {
                        var currentPrice = barsList.Last().Close;
                        var high52Week = barsList.TakeLast(252).Max(b => b.High);
                        var low52Week = barsList.TakeLast(252).Min(b => b.Low);
                        var high20Day = barsList.TakeLast(20).Max(b => b.High);
                        var low20Day = barsList.TakeLast(20).Min(b => b.Low);

                        return new
                        {
                            IsNewHigh52Week = currentPrice >= high52Week * 0.99m, // Within 1% of high
                            IsNewLow52Week = currentPrice <= low52Week * 1.01m,   // Within 1% of low
                            IsNewHigh20Day = currentPrice >= high20Day * 0.99m,
                            IsNewLow20Day = currentPrice <= low20Day * 1.01m
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Error calculating highs/lows for {Symbol}", symbol);
                }
                return new { IsNewHigh52Week = false, IsNewLow52Week = false, IsNewHigh20Day = false, IsNewLow20Day = false };
            });

            var results = await Task.WhenAll(tasks);

            newHighs52Week = results.Count(r => r.IsNewHigh52Week);
            newLows52Week = results.Count(r => r.IsNewLow52Week);
            newHighs20Day = results.Count(r => r.IsNewHigh20Day);
            newLows20Day = results.Count(r => r.IsNewLow20Day);

            var highLowRatio = newLows52Week > 0 ? (decimal)newHighs52Week / newLows52Week : newHighs52Week;
            var total = symbols.Count;
            var highLowPercent = total > 0 ? (decimal)(newHighs52Week - newLows52Week) / total * 100 : 0;

            return new NewHighsLowsStats
            {
                NewHighs52Week = newHighs52Week,
                NewLows52Week = newLows52Week,
                NewHighs20Day = newHighs20Day,
                NewLows20Day = newLows20Day,
                HighLowRatio = highLowRatio,
                HighLowPercent = highLowPercent,
                Trend = ClassifyNewHighsLowsTrend(highLowPercent)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating new highs/lows stats");
            return new NewHighsLowsStats();
        }
    }

    private async Task<BreadthMomentumAnalysis> CalculateBreadthMomentumAsync(
        List<string> symbols,
        CancellationToken cancellationToken)
    {
        // Simplified momentum calculation
        return new BreadthMomentumAnalysis
        {
            BreadthMomentum1Day = 0.5m,
            BreadthMomentum5Day = 0.3m,
            BreadthMomentum20Day = 0.2m,
            BreadthAcceleration = 0.1m,
            Direction = BreadthMomentumDirection.Bullish,
            Strength = BreadthMomentumStrength.Moderate
        };
    }

    private async Task<BreadthDivergenceAnalysis> CalculateBreadthDivergenceAsync(
        List<string> symbols,
        CancellationToken cancellationToken)
    {
        // Simplified divergence calculation
        return new BreadthDivergenceAnalysis
        {
            SpyBreadthDivergence = 0.1m,
            QqqBreadthDivergence = 0.05m,
            IwmBreadthDivergence = -0.1m,
            DivergenceType = BreadthDivergenceType.Neutral,
            Significance = BreadthDivergenceSignificance.Mild,
            IsWarningSignal = false
        };
    }

    private decimal CalculateOverallBreadthScore(
        AdvanceDeclineStats advanceDecline,
        MovingAverageBreadthStats movingAverage,
        NewHighsLowsStats newHighsLows,
        BreadthMomentumAnalysis momentum)
    {
        var score = 0m;

        // Advance/Decline component (40% weight)
        score += (advanceDecline.AdvanceDeclinePercent / 100m) * 0.4m;

        // Moving Average component (30% weight)
        score += (movingAverage.PercentAbove50SMA / 100m) * 0.3m;

        // New Highs/Lows component (20% weight)
        var highLowScore = Math.Max(0, (newHighsLows.HighLowPercent + 100) / 200m); // Normalize to 0-1
        score += highLowScore * 0.2m;

        // Momentum component (10% weight)
        var momentumScore = (momentum.BreadthMomentum1Day + 1) / 2m; // Normalize to 0-1
        score += momentumScore * 0.1m;

        return Math.Max(0, Math.Min(1, score));
    }

    private BreadthRegime ClassifyBreadthRegime(
        decimal breadthScore,
        AdvanceDeclineStats advanceDecline,
        MovingAverageBreadthStats movingAverage)
    {
        return breadthScore switch
        {
            >= 0.8m => BreadthRegime.Healthy,
            >= 0.6m => BreadthRegime.Recovering,
            >= 0.4m => BreadthRegime.Deteriorating,
            >= 0.2m => BreadthRegime.Weak,
            _ => BreadthRegime.Extreme
        };
    }

    private BreadthSignalStrength CalculateSignalStrength(decimal breadthScore, BreadthRegime regime)
    {
        return breadthScore switch
        {
            >= 0.8m => BreadthSignalStrength.VeryStrong,
            >= 0.6m => BreadthSignalStrength.Strong,
            >= 0.4m => BreadthSignalStrength.Neutral,
            >= 0.2m => BreadthSignalStrength.Weak,
            _ => BreadthSignalStrength.VeryWeak
        };
    }

    private string GenerateBreadthSummary(BreadthRegime regime, BreadthSignalStrength signalStrength, decimal breadthScore)
    {
        return $"Breadth regime: {regime}, Signal strength: {signalStrength}, Score: {breadthScore:F2}";
    }

    private decimal CalculateRegimeConfidence(RealTimeBreadthAnalysis analysis)
    {
        // Calculate confidence based on consistency across metrics
        var confidence = 0.5m;

        if (analysis.AdvanceDecline.AdvanceDeclinePercent > 60 || analysis.AdvanceDecline.AdvanceDeclinePercent < 40)
            confidence += 0.2m;

        if (analysis.MovingAverageBreadth.PercentAbove50SMA > 70 || analysis.MovingAverageBreadth.PercentAbove50SMA < 30)
            confidence += 0.2m;

        if (Math.Abs(analysis.NewHighsLows.HighLowPercent) > 20)
            confidence += 0.1m;

        return Math.Min(1.0m, confidence);
    }

    private async Task CheckForExtremeBreadthConditions(RealTimeBreadthAnalysis analysis)
    {
        if (analysis.OverallBreadthScore >= _config.ExtremeBreadthThreshold)
        {
            ExtremeBreadthDetected?.Invoke(this, new ExtremeBreadthEventArgs
            {
                ExtremeBreadthType = ExtremeBreadthType.ExtremelyBullish,
                BreadthValue = analysis.OverallBreadthScore,
                Threshold = _config.ExtremeBreadthThreshold,
                DetectedAt = DateTime.UtcNow,
                Description = "Extremely bullish breadth conditions detected"
            });
        }
        else if (analysis.OverallBreadthScore <= (1 - _config.ExtremeBreadthThreshold))
        {
            ExtremeBreadthDetected?.Invoke(this, new ExtremeBreadthEventArgs
            {
                ExtremeBreadthType = ExtremeBreadthType.ExtremelyBearish,
                BreadthValue = analysis.OverallBreadthScore,
                Threshold = 1 - _config.ExtremeBreadthThreshold,
                DetectedAt = DateTime.UtcNow,
                Description = "Extremely bearish breadth conditions detected"
            });
        }
    }

    private async Task CheckForDivergenceSignals(RealTimeBreadthAnalysis analysis)
    {
        if (analysis.Divergence.Significance >= BreadthDivergenceSignificance.Moderate)
        {
            BreadthDivergenceDetected?.Invoke(this, new BreadthDivergenceEventArgs
            {
                DivergenceType = analysis.Divergence.DivergenceType,
                Significance = analysis.Divergence.Significance,
                DivergenceScore = analysis.Divergence.SpyBreadthDivergence,
                IndexSymbol = "SPY",
                DetectedAt = DateTime.UtcNow
            });
        }
    }

    private static AdvanceDeclineTrend ClassifyAdvanceDeclineTrend(decimal advanceDeclinePercent)
    {
        return advanceDeclinePercent switch
        {
            >= 60m => AdvanceDeclineTrend.Improving,
            <= 40m => AdvanceDeclineTrend.Deteriorating,
            _ => AdvanceDeclineTrend.Stable
        };
    }

    private static MovingAverageTrend ClassifyMovingAverageTrend(decimal percentAboveSMA)
    {
        return percentAboveSMA switch
        {
            >= 70m => MovingAverageTrend.Improving,
            <= 30m => MovingAverageTrend.Deteriorating,
            _ => MovingAverageTrend.Stable
        };
    }

    private static NewHighsLowsTrend ClassifyNewHighsLowsTrend(decimal highLowPercent)
    {
        return highLowPercent switch
        {
            >= 10m => NewHighsLowsTrend.Improving,
            <= -10m => NewHighsLowsTrend.Deteriorating,
            _ => NewHighsLowsTrend.Stable
        };
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _updateTimer?.Dispose();
        _analysisLock?.Dispose();
        _disposed = true;
    }
}

/// <summary>
/// Helper class for breadth symbol data
/// </summary>
internal class BreadthSymbolData
{
    public string Symbol { get; set; } = string.Empty;
    public decimal LastPrice { get; set; }
    public decimal PreviousClose { get; set; }
    public decimal SMA50 { get; set; }
    public decimal SMA200 { get; set; }
    public decimal High52Week { get; set; }
    public decimal Low52Week { get; set; }
    public DateTime LastUpdated { get; set; }
}
