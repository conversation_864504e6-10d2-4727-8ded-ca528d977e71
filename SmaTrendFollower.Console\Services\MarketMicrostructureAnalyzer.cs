using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

// ExecutionStrategy and ExecutionUrgency are defined in IRealTimeExecutionService.cs

/// <summary>
/// Market impact risk levels
/// </summary>
public enum MarketImpactRisk
{
    Low,
    Medium,
    High,
    Extreme
}

/// <summary>
/// Interface for market microstructure analysis and execution optimization
/// </summary>
public interface IMarketMicrostructureAnalyzer
{
    /// <summary>
    /// Analyzes market microstructure for optimal execution
    /// </summary>
    Task<MarketMicrostructureAnalysis> AnalyzeMarketMicrostructureAsync(string symbol);

    /// <summary>
    /// Predicts optimal execution window for a trade
    /// </summary>
    Task<OptimalExecutionWindow> PredictOptimalExecutionWindowAsync(string symbol, decimal orderSize);

    /// <summary>
    /// Estimates market impact for a given order size
    /// </summary>
    Task<MarketImpactEstimate> EstimateMarketImpactAsync(string symbol, decimal orderSize);

    /// <summary>
    /// Analyzes order book depth and liquidity
    /// </summary>
    Task<OrderBookAnalysis> AnalyzeOrderBookAsync(string symbol);

    /// <summary>
    /// Calculates optimal order slicing strategy
    /// </summary>
    Task<OrderSlicingStrategy> CalculateOrderSlicingAsync(string symbol, decimal totalSize, TimeSpan timeHorizon);
}

/// <summary>
/// Market microstructure analyzer for execution optimization
/// </summary>
public sealed class MarketMicrostructureAnalyzer : IMarketMicrostructureAnalyzer
{
    private readonly ILogger<MarketMicrostructureAnalyzer> _logger;
    private readonly IMarketDataService _marketDataService;
    private readonly Random _random;

    public MarketMicrostructureAnalyzer(
        ILogger<MarketMicrostructureAnalyzer> logger,
        IMarketDataService marketDataService)
    {
        _logger = logger;
        _marketDataService = marketDataService;
        _random = new Random(42); // Fixed seed for reproducibility
    }

    public async Task<MarketMicrostructureAnalysis> AnalyzeMarketMicrostructureAsync(string symbol)
    {
        _logger.LogInformation("Analyzing market microstructure for {Symbol}", symbol);

        try
        {
            // Simulate microstructure data collection
            await Task.Delay(300);

            var spreadAnalysis = AnalyzeSpread(symbol);
            var liquidityPattern = AnalyzeLiquidity(symbol);
            var volumeProfile = AnalyzeVolumeProfile(symbol);
            var volatilityPattern = AnalyzeVolatilityPattern(symbol);

            var analysis = new MarketMicrostructureAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                Spread = spreadAnalysis.AverageSpread,
                SpreadPercent = spreadAnalysis.SpreadPercent,
                Quality = MicrostructureQuality.Good, // Default based on analysis
                LiquidityLevel = liquidityPattern.LiquidityScore > 0.7 ? LiquidityLevel.High : LiquidityLevel.Medium
            };

            _logger.LogInformation("Microstructure analysis completed for {Symbol}. Liquidity score: {Score:F2}", 
                symbol, liquidityPattern.LiquidityScore);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Microstructure analysis failed for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<OptimalExecutionWindow> PredictOptimalExecutionWindowAsync(string symbol, decimal orderSize)
    {
        _logger.LogInformation("Predicting optimal execution window for {Symbol}, size: {Size}", symbol, orderSize);

        try
        {
            await Task.Delay(200);

            var microstructure = await AnalyzeMarketMicrostructureAsync(symbol);
            var strategy = DetermineOptimalStrategy(microstructure, orderSize);
            var urgency = CalculateExecutionUrgency(microstructure, orderSize);
            var estimatedImpact = EstimateExecutionImpact(microstructure, orderSize);

            var window = new OptimalExecutionWindow(
                symbol,
                strategy,
                urgency,
                estimatedImpact,
                CalculateOptimalTimeWindow(),
                DateTime.UtcNow
            );

            _logger.LogInformation("Optimal execution window: {Strategy} with {Urgency} urgency, impact: {Impact:P3}",
                strategy, urgency, estimatedImpact);

            return window;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Execution window prediction failed for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<MarketImpactEstimate> EstimateMarketImpactAsync(string symbol, decimal orderSize)
    {
        _logger.LogInformation("Estimating market impact for {Symbol}, size: {Size}", symbol, orderSize);

        try
        {
            await Task.Delay(150);

            var microstructure = await AnalyzeMarketMicrostructureAsync(symbol);
            
            // Simplified market impact model
            var liquidityFactor = microstructure.LiquidityLevel == LiquidityLevel.VeryLow ? 0.8 :
                                 microstructure.LiquidityLevel == LiquidityLevel.Low ? 0.6 :
                                 microstructure.LiquidityLevel == LiquidityLevel.Medium ? 0.4 :
                                 microstructure.LiquidityLevel == LiquidityLevel.High ? 0.2 : 0.1;
            var sizeFactor = Math.Log(1 + (double)orderSize / 1000); // Size impact
            var volatilityFactor = (double)microstructure.SpreadPercent / 100.0; // Use spread as volatility proxy

            var temporaryImpact = (decimal)(liquidityFactor * sizeFactor * 0.001);
            var permanentImpact = temporaryImpact * 0.3m;
            var totalImpact = temporaryImpact + permanentImpact;

            var impactCost = orderSize * totalImpact * 100m; // Assuming $100 average price
            var riskLevel = DetermineRiskLevel(totalImpact);

            var estimate = new MarketImpactEstimate(
                symbol,
                orderSize,
                temporaryImpact,
                permanentImpact,
                totalImpact,
                impactCost,
                riskLevel,
                DateTime.UtcNow
            );

            _logger.LogInformation("Market impact estimated: {Impact:P3} total, cost: ${Cost:F2}", 
                totalImpact, impactCost);

            return estimate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Market impact estimation failed for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<OrderBookAnalysis> AnalyzeOrderBookAsync(string symbol)
    {
        _logger.LogInformation("Analyzing order book for {Symbol}", symbol);

        try
        {
            await Task.Delay(100);

            // Simulate order book analysis
            var bidDepth = GenerateOrderBookDepth(true);
            var askDepth = GenerateOrderBookDepth(false);
            var imbalance = CalculateOrderBookImbalance(bidDepth, askDepth);
            var liquidityScore = CalculateLiquidityScore(bidDepth, askDepth);

            var analysis = new OrderBookAnalysis(
                symbol,
                bidDepth,
                askDepth,
                imbalance,
                liquidityScore,
                DateTime.UtcNow
            );

            _logger.LogInformation("Order book analysis completed. Imbalance: {Imbalance:F2}, Liquidity: {Score:F2}",
                imbalance, liquidityScore);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Order book analysis failed for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<OrderSlicingStrategy> CalculateOrderSlicingAsync(string symbol, decimal totalSize, TimeSpan timeHorizon)
    {
        _logger.LogInformation("Calculating order slicing for {Symbol}, size: {Size}, horizon: {Horizon}",
            symbol, totalSize, timeHorizon);

        try
        {
            await Task.Delay(250);

            var microstructure = await AnalyzeMarketMicrostructureAsync(symbol);
            var optimalSlices = CalculateOptimalSlices(totalSize, timeHorizon, microstructure);
            var executionSchedule = CreateExecutionSchedule(optimalSlices, timeHorizon);

            var strategy = new OrderSlicingStrategy(
                symbol,
                totalSize,
                optimalSlices,
                executionSchedule,
                timeHorizon,
                DateTime.UtcNow
            );

            _logger.LogInformation("Order slicing calculated: {Slices} slices over {Duration}",
                optimalSlices.Count, timeHorizon);

            return strategy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Order slicing calculation failed for {Symbol}", symbol);
            throw;
        }
    }

    private SpreadAnalysis AnalyzeSpread(string symbol)
    {
        // Simulate spread analysis
        var averageSpread = 0.01m + (decimal)(_random.NextDouble() * 0.02); // 1-3 cents
        var spreadVolatility = averageSpread * 0.3m;
        
        return new SpreadAnalysis(averageSpread, spreadVolatility, DateTime.UtcNow);
    }

    private LiquidityPattern AnalyzeLiquidity(string symbol)
    {
        // Simulate liquidity analysis
        var liquidityScore = 0.6 + _random.NextDouble() * 0.4; // 0.6 to 1.0
        var averageVolume = 1000000 + _random.Next(5000000); // 1M to 6M shares
        
        return new LiquidityPattern(liquidityScore, averageVolume, DateTime.UtcNow);
    }

    private VolumeProfile AnalyzeVolumeProfile(string symbol)
    {
        // Simulate volume profile analysis
        var patterns = new[] { "Normal", "U-Shaped", "Reverse-U", "Trending", "Choppy" };
        var pattern = patterns[_random.Next(patterns.Length)];
        var concentration = 0.3 + _random.NextDouble() * 0.4; // 30-70% concentration
        
        return new VolumeProfile(pattern, concentration, DateTime.UtcNow);
    }

    private VolatilityPattern AnalyzeVolatilityPattern(string symbol)
    {
        // Simulate volatility analysis
        var currentVolatility = 0.15 + _random.NextDouble() * 0.25; // 15-40% annualized
        var regime = currentVolatility > 0.25 ? "High" : currentVolatility > 0.20 ? "Medium" : "Low";
        
        return new VolatilityPattern(currentVolatility, regime, DateTime.UtcNow);
    }

    private ExecutionStrategy DetermineOptimalStrategy(MarketMicrostructureAnalysis analysis, decimal orderSize)
    {
        if (analysis.LiquidityPattern.LiquidityScore > 0.8 && orderSize < 1000)
            return ExecutionStrategy.Market;
        
        if (analysis.VolatilityPattern.CurrentVolatility > 0.3)
            return ExecutionStrategy.TWAP;
        
        return ExecutionStrategy.VWAP;
    }

    private ExecutionUrgency CalculateExecutionUrgency(MarketMicrostructureAnalysis analysis, decimal orderSize)
    {
        var urgencyScore = analysis.VolatilityPattern.CurrentVolatility * 2 + 
                          (1 - analysis.LiquidityPattern.LiquidityScore);
        
        return urgencyScore switch
        {
            > 0.8 => ExecutionUrgency.Critical,
            > 0.6 => ExecutionUrgency.High,
            > 0.4 => ExecutionUrgency.Medium,
            _ => ExecutionUrgency.Low
        };
    }

    private decimal EstimateExecutionImpact(MarketMicrostructureAnalysis analysis, decimal orderSize)
    {
        var baseImpact = analysis.Spread / 2; // Half spread
        var sizeImpact = (decimal)Math.Log(1 + (double)orderSize / 10000) * 0.001m;
        var volatilityImpact = analysis.SpreadPercent * 0.002m; // Use spread percent as volatility proxy

        return baseImpact + sizeImpact + volatilityImpact;
    }

    private TimeSpan CalculateOptimalTimeWindow()
    {
        // Random time window between 5 minutes and 2 hours
        var minutes = 5 + _random.Next(115);
        return TimeSpan.FromMinutes(minutes);
    }

    private MarketImpactRisk DetermineRiskLevel(decimal totalImpact)
    {
        return totalImpact switch
        {
            > 0.01m => MarketImpactRisk.Extreme,
            > 0.005m => MarketImpactRisk.High,
            > 0.002m => MarketImpactRisk.Medium,
            _ => MarketImpactRisk.Low
        };
    }

    private List<OrderBookLevel> GenerateOrderBookDepth(bool isBid)
    {
        var levels = new List<OrderBookLevel>();
        var basePrice = 100m;
        var increment = isBid ? -0.01m : 0.01m;
        
        for (int i = 0; i < 10; i++)
        {
            var price = basePrice + (increment * i);
            var size = 1000 + _random.Next(5000);
            levels.Add(new OrderBookLevel(price, size));
        }
        
        return levels;
    }

    private decimal CalculateOrderBookImbalance(List<OrderBookLevel> bids, List<OrderBookLevel> asks)
    {
        var bidVolume = bids.Sum(b => b.Size);
        var askVolume = asks.Sum(a => a.Size);
        var totalVolume = bidVolume + askVolume;
        
        return totalVolume > 0 ? (bidVolume - askVolume) / totalVolume : 0;
    }

    private double CalculateLiquidityScore(List<OrderBookLevel> bids, List<OrderBookLevel> asks)
    {
        var totalDepth = bids.Sum(b => b.Size) + asks.Sum(a => a.Size);
        return Math.Min(1.0, totalDepth / 100000.0); // Normalize to 100k shares
    }

    private List<OrderSlice> CalculateOptimalSlices(decimal totalSize, TimeSpan timeHorizon, MarketMicrostructureAnalysis analysis)
    {
        var slices = new List<OrderSlice>();
        var numSlices = Math.Max(1, (int)(timeHorizon.TotalMinutes / 15)); // 15-minute slices
        var sliceSize = totalSize / numSlices;
        
        for (int i = 0; i < numSlices; i++)
        {
            slices.Add(new OrderSlice(
                i + 1,
                sliceSize,
                TimeSpan.FromMinutes(i * 15),
                ExecutionStrategy.VWAP
            ));
        }
        
        return slices;
    }

    private List<ScheduledExecution> CreateExecutionSchedule(List<OrderSlice> slices, TimeSpan timeHorizon)
    {
        var schedule = new List<ScheduledExecution>();
        var startTime = DateTime.UtcNow;
        
        foreach (var slice in slices)
        {
            schedule.Add(new ScheduledExecution(
                slice.SliceNumber,
                startTime.Add(slice.StartOffset),
                slice.Size,
                slice.Strategy
            ));
        }
        
        return schedule;
    }
}

// MarketMicrostructureAnalysis and SpreadAnalysis are defined in IRealTimeExecutionService.cs

/// <summary>
/// Liquidity pattern analysis
/// </summary>
public record LiquidityPattern(
    double LiquidityScore,
    int AverageVolume,
    DateTime AnalyzedAt
);

/// <summary>
/// Volume profile analysis
/// </summary>
public record VolumeProfile(
    string Pattern,
    double Concentration,
    DateTime AnalyzedAt
);

/// <summary>
/// Volatility pattern analysis
/// </summary>
public record VolatilityPattern(
    double CurrentVolatility,
    string Regime,
    DateTime AnalyzedAt
);

/// <summary>
/// Optimal execution window prediction
/// </summary>
public record OptimalExecutionWindow(
    string Symbol,
    ExecutionStrategy Strategy,
    ExecutionUrgency Urgency,
    decimal EstimatedImpact,
    TimeSpan OptimalWindow,
    DateTime PredictedAt
);

/// <summary>
/// Market impact estimate
/// </summary>
public record MarketImpactEstimate(
    string Symbol,
    decimal OrderSize,
    decimal TemporaryImpact,
    decimal PermanentImpact,
    decimal TotalImpact,
    decimal ImpactCost,
    MarketImpactRisk RiskLevel,
    DateTime EstimatedAt
);

/// <summary>
/// Order book analysis
/// </summary>
public record OrderBookAnalysis(
    string Symbol,
    List<OrderBookLevel> BidDepth,
    List<OrderBookLevel> AskDepth,
    decimal Imbalance,
    double LiquidityScore,
    DateTime AnalyzedAt
);

/// <summary>
/// Order book level
/// </summary>
public record OrderBookLevel(
    decimal Price,
    int Size
);

/// <summary>
/// Order slicing strategy
/// </summary>
public record OrderSlicingStrategy(
    string Symbol,
    decimal TotalSize,
    List<OrderSlice> Slices,
    List<ScheduledExecution> ExecutionSchedule,
    TimeSpan TimeHorizon,
    DateTime CreatedAt
);

/// <summary>
/// Individual order slice
/// </summary>
public record OrderSlice(
    int SliceNumber,
    decimal Size,
    TimeSpan StartOffset,
    ExecutionStrategy Strategy
);

/// <summary>
/// Scheduled execution
/// </summary>
public record ScheduledExecution(
    int SliceNumber,
    DateTime ExecutionTime,
    decimal Size,
    ExecutionStrategy Strategy
);
