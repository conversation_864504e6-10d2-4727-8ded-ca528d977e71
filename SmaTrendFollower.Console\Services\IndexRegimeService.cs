using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Index-based market regime service implementation
/// Uses I:SPX, I:VIX, I:NDX to classify market regimes in real time
/// Tracks SPX 1-day momentum, VIX levels, and SPX-VIX divergence
/// </summary>
public sealed class IndexRegimeService : IIndexRegimeService, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITickStreamService _tickStreamService;
    private readonly ILogger<IndexRegimeService> _logger;
    private readonly IndexRegimeConfig _config;
    
    private readonly Timer _regimeUpdateTimer;
    private readonly ConcurrentDictionary<string, decimal> _lastIndexPrices = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    
    private IndexMarketRegime _currentRegime = IndexMarketRegime.Sideways;
    private IndexRegimeMonitorStatus _status = IndexRegimeMonitorStatus.Stopped;
    private bool _disposed;

    public IndexRegimeService(
        IMarketDataService marketDataService,
        IOptimizedRedisConnectionService redisService,
        ITickStreamService tickStreamService,
        IConfiguration configuration,
        ILogger<IndexRegimeService> logger)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new IndexRegimeConfig();
        configuration.GetSection("IndexRegime").Bind(_config);

        // Set up timer for periodic regime updates
        _regimeUpdateTimer = new Timer(UpdateRegimeCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    // === Events ===
    
    public event EventHandler<IndexRegimeChangeEventArgs>? RegimeChanged;
    public event EventHandler<VixSpikeEventArgs>? VixSpikeDetected;
    public event EventHandler<SpxVixDivergenceEventArgs>? SpxVixDivergenceDetected;

    // === Core Methods ===

    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(IndexRegimeService));

        if (_status == IndexRegimeMonitorStatus.Active)
            return;

        try
        {
            _status = IndexRegimeMonitorStatus.Starting;
            _logger.LogInformation("Starting IndexRegimeService monitoring...");

            // Subscribe to tick stream for real-time index updates
            if (_config.EnableRealTimeUpdates)
            {
                await SubscribeToIndexTicksAsync();
            }

            // Perform initial regime analysis
            await RefreshRegimeAsync(cancellationToken);

            // Start periodic updates
            _regimeUpdateTimer.Change(
                TimeSpan.FromMinutes(_config.RegimeConfirmationMinutes),
                TimeSpan.FromMinutes(_config.RegimeConfirmationMinutes));

            _status = IndexRegimeMonitorStatus.Active;
            _logger.LogInformation("IndexRegimeService monitoring started successfully");
        }
        catch (Exception ex)
        {
            _status = IndexRegimeMonitorStatus.Error;
            _logger.LogError(ex, "Failed to start IndexRegimeService monitoring");
            throw;
        }
    }

    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || _status == IndexRegimeMonitorStatus.Stopped)
            return;

        try
        {
            _logger.LogInformation("Stopping IndexRegimeService monitoring...");

            // Stop timer
            _regimeUpdateTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // Unsubscribe from tick stream
            await UnsubscribeFromIndexTicksAsync();

            _status = IndexRegimeMonitorStatus.Stopped;
            _logger.LogInformation("IndexRegimeService monitoring stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping IndexRegimeService monitoring");
        }
    }

    public async Task<IndexMarketRegime> GetCurrentRegimeAsync(CancellationToken cancellationToken = default)
    {
        var cachedData = await GetCachedRegimeDataAsync(cancellationToken);
        if (cachedData != null && IsCacheValid(cachedData))
        {
            return cachedData.Regime;
        }

        return await RefreshRegimeAsync(cancellationToken);
    }

    public async Task<IndexMarketRegime?> GetCachedRegimeAsync(CancellationToken cancellationToken = default)
    {
        var regimeData = await GetCachedRegimeDataAsync(cancellationToken);
        return regimeData?.Regime;
    }

    private async Task<RedisIndexRegime?> GetCachedRegimeDataAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var regimeJson = await database.StringGetAsync(RedisIndexRegime.GetRedisKey());

            if (!regimeJson.HasValue)
                return null;

            return RedisIndexRegime.FromJson(regimeJson!);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get cached index regime");
            return null;
        }
    }

    public async Task<IndexMarketRegime> RefreshRegimeAsync(CancellationToken cancellationToken = default)
    {
        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Refreshing index regime analysis...");

            // Get all required analyses
            var spxMomentum = await GetSpxMomentumAsync(cancellationToken);
            var vixAnalysis = await GetVixAnalysisAsync(cancellationToken);
            var divergence = await GetSpxVixDivergenceAsync(cancellationToken);
            var ndxMomentum = await GetNdxMomentumAsync(cancellationToken);

            // Classify regime based on all factors
            var newRegime = ClassifyRegime(spxMomentum, vixAnalysis, divergence, ndxMomentum);
            var confidence = CalculateConfidence(spxMomentum, vixAnalysis, divergence);

            // Check for regime change
            if (newRegime != _currentRegime)
            {
                var previousRegime = _currentRegime;
                _currentRegime = newRegime;

                RegimeChanged?.Invoke(this, new IndexRegimeChangeEventArgs
                {
                    PreviousRegime = previousRegime,
                    NewRegime = newRegime,
                    ChangedAt = DateTime.UtcNow,
                    Confidence = confidence,
                    Reason = $"SPX: {spxMomentum.MomentumPercent:F2}%, VIX: {vixAnalysis.CurrentVix:F1}, Divergence: {divergence.DivergenceScore:F2}"
                });

                _logger.LogInformation("Index regime changed from {Previous} to {New} (Confidence: {Confidence:P1})",
                    previousRegime, newRegime, confidence);
            }

            // Cache the result
            await CacheRegimeAsync(newRegime, confidence, spxMomentum, vixAnalysis, divergence, ndxMomentum);

            return newRegime;
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    // === Analysis Methods ===

    public async Task<SpxMomentumAnalysis> GetSpxMomentumAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddMinutes(-_config.SpxMomentumPeriodMinutes);

            var bars = await _marketDataService.GetIndexBarsAsync("SPX", startTime, endTime, cancellationToken);
            var barsList = bars.ToList();

            if (barsList.Count < 2)
            {
                _logger.LogWarning("Insufficient SPX data for momentum analysis");
                return new SpxMomentumAnalysis
                {
                    AnalyzedAt = DateTime.UtcNow,
                    Direction = MomentumDirection.Neutral,
                    Strength = MomentumStrength.Weak
                };
            }

            var currentBar = barsList.Last();
            var previousBar = barsList[^2];

            var momentumPercent = ((currentBar.Close - previousBar.Close) / previousBar.Close) * 100;
            var intraDayRange = ((currentBar.High - currentBar.Low) / currentBar.Low) * 100;

            return new SpxMomentumAnalysis
            {
                CurrentPrice = currentBar.Close,
                PreviousPrice = previousBar.Close,
                MomentumPercent = momentumPercent,
                IntraDayHigh = currentBar.High,
                IntraDayLow = currentBar.Low,
                IntraDayRange = intraDayRange,
                AnalyzedAt = DateTime.UtcNow,
                Direction = ClassifyMomentumDirection(momentumPercent),
                Strength = ClassifyMomentumStrength(Math.Abs(momentumPercent))
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing SPX momentum");
            throw;
        }
    }

    public async Task<VixAnalysis> GetVixAnalysisAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddDays(-1);

            var bars = await _marketDataService.GetIndexBarsAsync("VIX", startTime, endTime, cancellationToken);
            var barsList = bars.ToList();

            if (barsList.Count < 2)
            {
                _logger.LogWarning("Insufficient VIX data for analysis");
                return new VixAnalysis
                {
                    AnalyzedAt = DateTime.UtcNow,
                    Level = VixLevel.Normal,
                    Trend = VixTrend.Stable
                };
            }

            var currentBar = barsList.Last();
            var previousBar = barsList[^2];

            var vixChange = currentBar.Close - previousBar.Close;
            var vixChangePercent = (vixChange / previousBar.Close) * 100;

            // Calculate 20-day average (approximate with available data)
            var twentyDayAvg = barsList.TakeLast(Math.Min(20, barsList.Count)).Average(b => b.Close);

            var analysis = new VixAnalysis
            {
                CurrentVix = currentBar.Close,
                PreviousVix = previousBar.Close,
                VixChange = vixChange,
                VixChangePercent = vixChangePercent,
                DayHigh = currentBar.High,
                DayLow = currentBar.Low,
                TwentyDayAverage = twentyDayAvg,
                AnalyzedAt = DateTime.UtcNow,
                Level = ClassifyVixLevel(currentBar.Close),
                Trend = ClassifyVixTrend(vixChangePercent)
            };

            // Check for VIX spike
            if (Math.Abs(vixChangePercent) > 10) // 10% change threshold
            {
                VixSpikeDetected?.Invoke(this, new VixSpikeEventArgs
                {
                    CurrentVix = currentBar.Close,
                    PreviousVix = previousBar.Close,
                    SpikePercentage = vixChangePercent,
                    DetectedAt = DateTime.UtcNow,
                    SpikeType = ClassifyVixSpike(Math.Abs(vixChangePercent))
                });
            }

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing VIX");
            throw;
        }
    }

    public async Task<SpxVixDivergenceAnalysis> GetSpxVixDivergenceAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var spxMomentum = await GetSpxMomentumAsync(cancellationToken);
            var vixAnalysis = await GetVixAnalysisAsync(cancellationToken);

            var spxChange = spxMomentum.MomentumPercent;
            var vixChange = vixAnalysis.VixChangePercent;

            // Calculate divergence score (higher = more divergent)
            var expectedVixChange = -spxChange * 0.5m; // VIX typically moves opposite to SPX
            var divergenceScore = Math.Abs(vixChange - expectedVixChange);

            var divergenceType = ClassifyDivergenceType(spxChange, vixChange);
            var significance = ClassifyDivergenceSignificance(divergenceScore);

            var analysis = new SpxVixDivergenceAnalysis
            {
                SpxChange = spxChange,
                VixChange = vixChange,
                DivergenceScore = divergenceScore,
                CorrelationCoefficient = CalculateCorrelation(spxChange, vixChange),
                AnalyzedAt = DateTime.UtcNow,
                DivergenceType = divergenceType,
                Significance = significance,
                IsAnomalous = divergenceScore > _config.DivergenceThreshold
            };

            // Fire divergence event if significant
            if (significance >= DivergenceSignificance.Moderate)
            {
                SpxVixDivergenceDetected?.Invoke(this, new SpxVixDivergenceEventArgs
                {
                    SpxChange = spxChange,
                    VixChange = vixChange,
                    DivergenceScore = divergenceScore,
                    DetectedAt = DateTime.UtcNow,
                    DivergenceType = divergenceType
                });
            }

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing SPX-VIX divergence");
            throw;
        }
    }

    public async Task<NdxMomentumAnalysis> GetNdxMomentumAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddMinutes(-_config.SpxMomentumPeriodMinutes);

            var ndxBars = await _marketDataService.GetIndexBarsAsync("NDX", startTime, endTime, cancellationToken);
            var ndxBarsList = ndxBars.ToList();

            if (ndxBarsList.Count < 2)
            {
                _logger.LogWarning("Insufficient NDX data for momentum analysis");
                return new NdxMomentumAnalysis
                {
                    AnalyzedAt = DateTime.UtcNow,
                    Direction = MomentumDirection.Neutral,
                    TechStrength = TechSectorStrength.InLine
                };
            }

            var currentBar = ndxBarsList.Last();
            var previousBar = ndxBarsList[^2];

            var momentumPercent = ((currentBar.Close - previousBar.Close) / previousBar.Close) * 100;

            // Get SPX momentum for comparison
            var spxMomentum = await GetSpxMomentumAsync(cancellationToken);
            var relativeToSpx = momentumPercent - spxMomentum.MomentumPercent;

            return new NdxMomentumAnalysis
            {
                CurrentPrice = currentBar.Close,
                PreviousPrice = previousBar.Close,
                MomentumPercent = momentumPercent,
                RelativeToSpx = relativeToSpx,
                AnalyzedAt = DateTime.UtcNow,
                Direction = ClassifyMomentumDirection(momentumPercent),
                TechStrength = ClassifyTechStrength(relativeToSpx)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing NDX momentum");
            throw;
        }
    }

    // === Status and Configuration ===

    public IndexRegimeMonitorStatus GetStatus() => _status;

    public async Task UpdateConfigurationAsync(IndexRegimeConfig config)
    {
        // Update configuration (implementation would update _config)
        await Task.CompletedTask;
    }

    public async Task<IEnumerable<IndexRegimeHistoryEntry>> GetRegimeHistoryAsync(int hours = 24)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var history = new List<IndexRegimeHistoryEntry>();

            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-hours);

            // Get historical regime entries (simplified implementation)
            for (var time = startTime; time <= endTime; time = time.AddMinutes(15))
            {
                var key = RedisIndexRegime.GetRedisKey(time);
                var regimeJson = await database.StringGetAsync(key);
                
                if (regimeJson.HasValue)
                {
                    var regimeData = RedisIndexRegime.FromJson(regimeJson!);
                    if (regimeData != null)
                    {
                        history.Add(new IndexRegimeHistoryEntry
                        {
                            Regime = regimeData.Regime,
                            DetectedAt = regimeData.DetectedAt,
                            Confidence = regimeData.Confidence,
                            SpxMomentum = regimeData.SpxMomentum,
                            VixAnalysis = regimeData.VixAnalysis,
                            Divergence = regimeData.Divergence,
                            Metadata = regimeData.Metadata
                        });
                    }
                }
            }

            return history.OrderBy(h => h.DetectedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving regime history");
            return Enumerable.Empty<IndexRegimeHistoryEntry>();
        }
    }

    // === Private Methods ===

    private async void UpdateRegimeCallback(object? state)
    {
        try
        {
            await RefreshRegimeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in regime update callback");
        }
    }

    private async Task SubscribeToIndexTicksAsync()
    {
        // Subscribe to index ticks for real-time updates
        // Implementation would subscribe to I:SPX, I:VIX, I:NDX through TickStreamService
        await Task.CompletedTask;
    }

    private async Task UnsubscribeFromIndexTicksAsync()
    {
        // Unsubscribe from index ticks
        await Task.CompletedTask;
    }

    private bool IsCacheValid(RedisIndexRegime regimeData)
    {
        var age = DateTime.UtcNow - regimeData.DetectedAt;
        return age.TotalMinutes <= _config.CacheExpiryMinutes;
    }

    private async Task CacheRegimeAsync(
        IndexMarketRegime regime,
        decimal confidence,
        SpxMomentumAnalysis spxMomentum,
        VixAnalysis vixAnalysis,
        SpxVixDivergenceAnalysis divergence,
        NdxMomentumAnalysis ndxMomentum)
    {
        try
        {
            var regimeData = new RedisIndexRegime
            {
                Regime = regime,
                DetectedAt = DateTime.UtcNow,
                Confidence = confidence,
                SpxMomentum = spxMomentum,
                VixAnalysis = vixAnalysis,
                Divergence = divergence,
                NdxMomentum = ndxMomentum,
                Metadata = $"SPX: {spxMomentum.MomentumPercent:F2}%, VIX: {vixAnalysis.CurrentVix:F1}, NDX: {ndxMomentum.MomentumPercent:F2}%"
            };

            var database = await _redisService.GetDatabaseAsync();
            await database.StringSetAsync(
                RedisIndexRegime.GetRedisKey(),
                regimeData.ToJson(),
                TimeSpan.FromMinutes(_config.CacheExpiryMinutes));

            // Also store historical entry
            await database.StringSetAsync(
                RedisIndexRegime.GetRedisKey(DateTime.UtcNow),
                regimeData.ToJson(),
                TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache index regime data");
        }
    }

    private IndexMarketRegime ClassifyRegime(
        SpxMomentumAnalysis spxMomentum,
        VixAnalysis vixAnalysis,
        SpxVixDivergenceAnalysis divergence,
        NdxMomentumAnalysis ndxMomentum)
    {
        // Panic conditions: High VIX + negative SPX momentum
        if (vixAnalysis.CurrentVix > _config.VixPanicThreshold && spxMomentum.MomentumPercent < -1.0m)
        {
            return IndexMarketRegime.Panic;
        }

        // Euphoric conditions: Very low VIX + strong positive momentum
        if (vixAnalysis.CurrentVix < _config.VixEuphoricThreshold && spxMomentum.MomentumPercent > _config.SpxMomentumBullThreshold)
        {
            return IndexMarketRegime.Euphoric;
        }

        // Volatile conditions: High VIX
        if (vixAnalysis.CurrentVix > _config.VixVolatileThreshold)
        {
            return IndexMarketRegime.Volatile;
        }

        // Trending up: Positive momentum + low/normal VIX
        if (spxMomentum.MomentumPercent > _config.SpxMomentumBullThreshold && vixAnalysis.CurrentVix < 20m)
        {
            return IndexMarketRegime.TrendingUp;
        }

        // Trending down: Negative momentum
        if (spxMomentum.MomentumPercent < _config.SpxMomentumBearThreshold)
        {
            return IndexMarketRegime.TrendingDown;
        }

        // Default to sideways
        return IndexMarketRegime.Sideways;
    }

    private decimal CalculateConfidence(
        SpxMomentumAnalysis spxMomentum,
        VixAnalysis vixAnalysis,
        SpxVixDivergenceAnalysis divergence)
    {
        var confidence = 0.5m; // Base confidence

        // Strong momentum increases confidence
        if (Math.Abs(spxMomentum.MomentumPercent) > 1.0m)
            confidence += 0.2m;

        // Clear VIX signals increase confidence
        if (vixAnalysis.CurrentVix > 30m || vixAnalysis.CurrentVix < 15m)
            confidence += 0.2m;

        // Low divergence increases confidence
        if (divergence.DivergenceScore < 1.0m)
            confidence += 0.1m;

        return Math.Min(1.0m, confidence);
    }

    private static MomentumDirection ClassifyMomentumDirection(decimal momentumPercent)
    {
        return momentumPercent switch
        {
            > 0.1m => MomentumDirection.Bullish,
            < -0.1m => MomentumDirection.Bearish,
            _ => MomentumDirection.Neutral
        };
    }

    private static MomentumStrength ClassifyMomentumStrength(decimal absMomentumPercent)
    {
        return absMomentumPercent switch
        {
            > 2.0m => MomentumStrength.Extreme,
            > 1.0m => MomentumStrength.Strong,
            > 0.5m => MomentumStrength.Moderate,
            _ => MomentumStrength.Weak
        };
    }

    private static VixLevel ClassifyVixLevel(decimal vix)
    {
        return vix switch
        {
            < 12m => VixLevel.VeryLow,
            < 16m => VixLevel.Low,
            < 20m => VixLevel.Normal,
            < 25m => VixLevel.Elevated,
            < 30m => VixLevel.High,
            < 35m => VixLevel.VeryHigh,
            _ => VixLevel.Extreme
        };
    }

    private static VixTrend ClassifyVixTrend(decimal vixChangePercent)
    {
        return vixChangePercent switch
        {
            > 15m => VixTrend.Spiking,
            > 5m => VixTrend.Rising,
            < -5m => VixTrend.Declining,
            _ => VixTrend.Stable
        };
    }

    private static VixSpikeType ClassifyVixSpike(decimal absChangePercent)
    {
        return absChangePercent switch
        {
            > 30m => VixSpikeType.Extreme,
            > 20m => VixSpikeType.Severe,
            > 15m => VixSpikeType.Moderate,
            _ => VixSpikeType.Mild
        };
    }

    private static DivergenceType ClassifyDivergenceType(decimal spxChange, decimal vixChange)
    {
        if (spxChange > 0.5m && vixChange < -5m)
            return DivergenceType.Bullish;
        if (spxChange < -0.5m && vixChange > 5m)
            return DivergenceType.Bearish;
        return DivergenceType.Neutral;
    }

    private static DivergenceSignificance ClassifyDivergenceSignificance(decimal divergenceScore)
    {
        return divergenceScore switch
        {
            > 5.0m => DivergenceSignificance.Extreme,
            > 3.0m => DivergenceSignificance.Strong,
            > 2.0m => DivergenceSignificance.Moderate,
            > 1.0m => DivergenceSignificance.Mild,
            _ => DivergenceSignificance.Insignificant
        };
    }

    private static TechSectorStrength ClassifyTechStrength(decimal relativeToSpx)
    {
        return relativeToSpx switch
        {
            > 1.0m => TechSectorStrength.Leading,
            > 0.2m => TechSectorStrength.Outperforming,
            < -0.2m => TechSectorStrength.Underperforming,
            _ => TechSectorStrength.InLine
        };
    }

    private static decimal CalculateCorrelation(decimal spxChange, decimal vixChange)
    {
        // Simplified correlation calculation
        // In a real implementation, this would use historical data
        return spxChange * vixChange < 0 ? -0.7m : 0.3m;
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _regimeUpdateTimer?.Dispose();
        _analysisLock?.Dispose();
        _disposed = true;
    }
}
