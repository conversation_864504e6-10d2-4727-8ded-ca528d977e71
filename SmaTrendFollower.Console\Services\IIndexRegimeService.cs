using SmaTrendFollower.Models;
using ModelsVixAnalysis = SmaTrendFollower.Models.VixAnalysis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Index-based market regime service for real-time regime classification
/// Uses I:SPX, I:VIX, I:NDX to classify market regimes in real time
/// Tracks SPX 1-day momentum, VIX levels, and SPX-VIX divergence
/// Stores current regime in Redis (e.g. trending_up, volatile, panic)
/// </summary>
public interface IIndexRegimeService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when market regime changes
    /// </summary>
    event EventHandler<IndexRegimeChangeEventArgs>? RegimeChanged;
    
    /// <summary>
    /// Fired when VIX spike is detected
    /// </summary>
    event EventHandler<VixSpikeEventArgs>? VixSpikeDetected;
    
    /// <summary>
    /// Fired when SPX-VIX divergence is detected
    /// </summary>
    event EventHandler<SpxVixDivergenceEventArgs>? SpxVixDivergenceDetected;
    
    // === Core Methods ===
    
    /// <summary>
    /// Start real-time index regime monitoring
    /// </summary>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop real-time index regime monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current index-based market regime
    /// </summary>
    Task<IndexMarketRegime> GetCurrentRegimeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get cached index regime from Redis
    /// </summary>
    Task<IndexMarketRegime?> GetCachedRegimeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Force regime refresh and recalculation
    /// </summary>
    Task<IndexMarketRegime> RefreshRegimeAsync(CancellationToken cancellationToken = default);
    
    // === Analysis Methods ===
    
    /// <summary>
    /// Get SPX 1-day momentum analysis
    /// </summary>
    Task<SpxMomentumAnalysis> GetSpxMomentumAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get current VIX analysis
    /// </summary>
    Task<ModelsVixAnalysis> GetVixAnalysisAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get SPX-VIX divergence analysis
    /// </summary>
    Task<SpxVixDivergenceAnalysis> GetSpxVixDivergenceAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get NDX momentum for tech sector analysis
    /// </summary>
    Task<NdxMomentumAnalysis> GetNdxMomentumAsync(CancellationToken cancellationToken = default);
    
    // === Status and Configuration ===
    
    /// <summary>
    /// Get current monitoring status
    /// </summary>
    IndexRegimeMonitorStatus GetStatus();
    
    /// <summary>
    /// Update configuration
    /// </summary>
    Task UpdateConfigurationAsync(IndexRegimeConfig config);
    
    /// <summary>
    /// Get regime history for analysis
    /// </summary>
    Task<IEnumerable<IndexRegimeHistoryEntry>> GetRegimeHistoryAsync(int hours = 24);
}

/// <summary>
/// Index-based market regime classification
/// </summary>
public enum IndexMarketRegime
{
    /// <summary>
    /// Strong bullish trend with low VIX and positive SPX momentum
    /// </summary>
    TrendingUp,
    
    /// <summary>
    /// Bearish trend with negative SPX momentum
    /// </summary>
    TrendingDown,
    
    /// <summary>
    /// Range-bound market with neutral momentum
    /// </summary>
    Sideways,
    
    /// <summary>
    /// High volatility with elevated VIX
    /// </summary>
    Volatile,
    
    /// <summary>
    /// Panic conditions with VIX spike and negative momentum
    /// </summary>
    Panic,
    
    /// <summary>
    /// Euphoric conditions with very low VIX and strong momentum
    /// </summary>
    Euphoric
}

/// <summary>
/// Index regime monitoring status
/// </summary>
public enum IndexRegimeMonitorStatus
{
    Stopped,
    Starting,
    Active,
    Error
}

/// <summary>
/// Configuration for index regime service
/// </summary>
public record IndexRegimeConfig(
    int SpxMomentumPeriodMinutes = 1440, // 24 hours
    decimal VixVolatileThreshold = 25.0m,
    decimal VixPanicThreshold = 35.0m,
    decimal VixEuphoricThreshold = 12.0m,
    decimal SpxMomentumBullThreshold = 0.5m,
    decimal SpxMomentumBearThreshold = -0.5m,
    decimal DivergenceThreshold = 2.0m,
    int RegimeConfirmationMinutes = 15,
    int CacheExpiryMinutes = 5,
    bool EnableRealTimeUpdates = true
);

/// <summary>
/// Event args for regime change
/// </summary>
public class IndexRegimeChangeEventArgs : EventArgs
{
    public IndexMarketRegime PreviousRegime { get; set; }
    public IndexMarketRegime NewRegime { get; set; }
    public DateTime ChangedAt { get; set; }
    public decimal Confidence { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Event args for VIX spike detection
/// </summary>
public class VixSpikeEventArgs : EventArgs
{
    public decimal CurrentVix { get; set; }
    public decimal PreviousVix { get; set; }
    public decimal SpikePercentage { get; set; }
    public DateTime DetectedAt { get; set; }
    public VixSpikeType SpikeType { get; set; }
}

/// <summary>
/// Event args for SPX-VIX divergence
/// </summary>
public class SpxVixDivergenceEventArgs : EventArgs
{
    public decimal SpxChange { get; set; }
    public decimal VixChange { get; set; }
    public decimal DivergenceScore { get; set; }
    public DateTime DetectedAt { get; set; }
    public DivergenceType DivergenceType { get; set; }
}

/// <summary>
/// VIX spike types
/// </summary>
public enum VixSpikeType
{
    Mild,
    Moderate,
    Severe,
    Extreme
}

/// <summary>
/// Divergence types
/// </summary>
public enum DivergenceType
{
    Bullish, // SPX up, VIX down
    Bearish, // SPX down, VIX up
    Neutral
}
